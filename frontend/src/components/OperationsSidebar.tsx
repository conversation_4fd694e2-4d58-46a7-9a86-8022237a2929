import React from 'react';
import {
  Box,
  <PERSON>,
  Typo<PERSON>,
  <PERSON>ton,
  Divider,
  <PERSON>,
  IconButton,
  Tooltip,
  Collapse,
  List,
  ListItem,
  ListItemIcon,
  Badge
} from '@mui/material';
import {
  Compress,
  SelectAll,
  Clear,
  Delete,
  CleaningServices,
  ExpandMore,
  ExpandLess,
  PhotoLibrary
} from '@mui/icons-material';
import GooglePhotosButton from './GooglePhotosButton';
import Subscribe from '../Subscribe';

interface OperationsSidebarProps {
  // Selection state
  selectedItems: Set<string>;
  totalItems: number;
  expiredItemsCount: number;

  // Handlers for operations
  onSelectAll: () => void;
  onClearSelection: () => void;
  onCompressSelected: () => void;
  onRemoveSelected: () => void;
  onClearExpired: () => void;

  // Media loading
  onLoadFromGooglePhotos: () => void;
  isLoading?: boolean;
  onCancelLoading?: () => void;

  // UI state
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const OperationsSidebar: React.FC<OperationsSidebarProps> = ({
  selectedItems,
  totalItems,
  expiredItemsCount,
  onSelectAll,
  onClearSelection,
  onCompressSelected,
  onRemoveSelected,
  onClearExpired,
  onLoadFromGooglePhotos,
  isLoading = false,
  onCancelLoading,
  isCollapsed = false,
  onToggleCollapse
}) => {
  const selectedCount = selectedItems.size;
  const hasSelection = selectedCount > 0;
  const hasExpiredItems = expiredItemsCount > 0;

  return (
    <Paper
      elevation={0}
      sx={{
        width: isCollapsed ? 48 : 240,
        height: 'fit-content',
        maxHeight: 'calc(100vh - 200px)',
        position: 'sticky',
        top: 20,
        backgroundColor: 'background.paper',
        border: 1,
        borderColor: 'divider',
        borderRadius: 2,
        transition: 'width 0.3s ease',
        overflow: 'hidden',
        boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)'
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}
      >
        {!isCollapsed && (
          <Typography variant="h6" sx={{ fontWeight: 500, fontSize: '1.1rem' }}>
            Operations
          </Typography>
        )}
        {onToggleCollapse && (
          <IconButton
            onClick={onToggleCollapse}
            size="small"
            sx={{ ml: isCollapsed ? 0 : 'auto' }}
          >
            {isCollapsed ? <ExpandMore /> : <ExpandLess />}
          </IconButton>
        )}
      </Box>

      <Collapse in={!isCollapsed} timeout={300}>
        <Box sx={{ p: 2 }}>
          {/* Media Loading Section */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 500, color: 'text.secondary' }}>
              Media Library
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {isLoading ? (
                <Button
                  variant="contained"
                  disabled={true}
                  sx={{
                    backgroundColor: 'action.disabledBackground',
                    color: 'action.disabled',
                    textTransform: 'none',
                    fontWeight: 500,
                    borderRadius: 1,
                    py: 1
                  }}
                >
                  Loading media...
                </Button>
              ) : (
                <GooglePhotosButton
                  onClick={onLoadFromGooglePhotos}
                  size="medium"
                />
              )}
              {isLoading && onCancelLoading && (
                <Button
                  variant="outlined"
                  size="small"
                  onClick={onCancelLoading}
                  sx={{
                    borderColor: 'error.main',
                    color: 'error.main',
                    textTransform: 'none',
                    '&:hover': {
                      borderColor: 'error.dark',
                      backgroundColor: 'error.light',
                      color: 'error.dark'
                    },
                    borderRadius: 1
                  }}
                >
                  Cancel
                </Button>
              )}
              <Chip
                label={`${totalItems} items loaded`}
                size="small"
                variant="outlined"
                sx={{
                  borderColor: 'success.main',
                  color: 'success.dark',
                  backgroundColor: 'rgba(52, 168, 83, 0.1)',
                  fontWeight: 500,
                  '& .MuiChip-label': {
                    color: 'success.dark'
                  }
                }}
              />
              <Subscribe />
            </Box>
          </Box>

          <Divider sx={{ mb: 3 }} />

          {/* Selection Status */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 500, color: 'text.secondary' }}>
              Selection
            </Typography>
            <Chip
              label={hasSelection ? `${selectedCount} selected` : 'None selected'}
              size="small"
              variant={hasSelection ? 'filled' : 'outlined'}
              color={hasSelection ? 'primary' : 'default'}
              sx={{ mb: 2 }}
            />
            
            {/* Selection Controls */}
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<SelectAll />}
                onClick={onSelectAll}
                disabled={totalItems === 0}
                sx={{
                  flex: 1,
                  textTransform: 'none',
                  fontSize: '0.75rem'
                }}
              >
                All
              </Button>
              <Button
                variant="outlined"
                size="small"
                startIcon={<Clear />}
                onClick={onClearSelection}
                disabled={!hasSelection}
                sx={{
                  flex: 1,
                  textTransform: 'none',
                  fontSize: '0.75rem'
                }}
              >
                Clear
              </Button>
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Main Operations */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 500, color: 'text.secondary' }}>
              Actions
            </Typography>
            
            <List dense sx={{ p: 0 }}>
              {/* Compress Operation */}
              <ListItem sx={{ p: 0, mb: 1 }}>
                <Button
                  variant="contained"
                  fullWidth
                  startIcon={<Compress />}
                  onClick={onCompressSelected}
                  disabled={!hasSelection}
                  sx={{
                    justifyContent: 'flex-start',
                    textTransform: 'none',
                    py: 1.5,
                    backgroundColor: hasSelection ? 'primary.main' : 'action.disabledBackground',
                    borderRadius: 1,
                    boxShadow: hasSelection ? '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)' : 'none',
                    '&:hover': {
                      backgroundColor: hasSelection ? 'primary.dark' : 'action.disabledBackground',
                      boxShadow: hasSelection ? '0 1px 3px 0 rgba(60,64,67,.3), 0 4px 8px 3px rgba(60,64,67,.15)' : 'none'
                    }
                  }}
                >
                  Compress {hasSelection ? `(${selectedCount})` : ''}
                </Button>
              </ListItem>

              {/* Remove Access Operation */}
              <ListItem sx={{ p: 0, mb: 1 }}>
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<Delete />}
                  onClick={onRemoveSelected}
                  disabled={!hasSelection}
                  color="error"
                  sx={{
                    justifyContent: 'flex-start',
                    textTransform: 'none',
                    py: 1.5,
                    borderRadius: 1,
                    borderColor: hasSelection ? 'error.main' : 'action.disabled',
                    color: hasSelection ? 'error.main' : 'action.disabled',
                    '&:hover': {
                      backgroundColor: hasSelection ? 'rgba(234, 67, 53, 0.08)' : 'transparent',
                      borderColor: hasSelection ? 'error.dark' : 'action.disabled'
                    }
                  }}
                >
                  Remove Access {hasSelection ? `(${selectedCount})` : ''}
                </Button>
              </ListItem>
            </List>
          </Box>

          {/* Maintenance Operations */}
          {hasExpiredItems && (
            <>
              <Divider sx={{ my: 2 }} />
              <Box>
                <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 500, color: 'text.secondary' }}>
                  Maintenance
                </Typography>
                
                <List dense sx={{ p: 0 }}>
                  <ListItem sx={{ p: 0 }}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<CleaningServices />}
                      onClick={onClearExpired}
                      color="warning"
                      sx={{
                        justifyContent: 'flex-start',
                        textTransform: 'none',
                        py: 1.5,
                        borderRadius: 1,
                        '&:hover': {
                          backgroundColor: 'rgba(255, 193, 7, 0.08)',
                          borderColor: 'warning.dark'
                        }
                      }}
                    >
                      Clear Expired ({expiredItemsCount})
                    </Button>
                  </ListItem>
                </List>
              </Box>
            </>
          )}

          {/* Future Operations Placeholder */}
          <Divider sx={{ my: 2 }} />
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 500, color: 'text.secondary' }}>
              More Operations
            </Typography>
            <Typography variant="caption" sx={{ color: 'text.secondary', fontStyle: 'italic' }}>
              Additional features coming soon...
            </Typography>
          </Box>
        </Box>
      </Collapse>

      {/* Collapsed State */}
      {isCollapsed && (
        <Box sx={{ p: 1, display: 'flex', flexDirection: 'column', gap: 1.5 }}>
          {/* Media Loading */}
          <Tooltip title={`${totalItems} items loaded`} placement="right">
            <IconButton
              size="small"
              onClick={onLoadFromGooglePhotos}
              disabled={isLoading}
              color="default"
              sx={{ alignSelf: 'center' }}
            >
              <Badge badgeContent={totalItems} color="success" max={99}>
                <PhotoLibrary />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* Selection */}
          <Tooltip title={`${selectedCount} selected`} placement="right">
            <IconButton
              size="small"
              color={hasSelection ? 'primary' : 'default'}
              sx={{ alignSelf: 'center' }}
            >
              <Badge badgeContent={selectedCount} color="primary" max={99}>
                <SelectAll />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* Compress */}
          <Tooltip title="Compress Selected" placement="right">
            <IconButton
              size="small"
              onClick={onCompressSelected}
              disabled={!hasSelection}
              color="primary"
              sx={{ alignSelf: 'center' }}
            >
              <Compress />
            </IconButton>
          </Tooltip>

          {/* Remove Access */}
          <Tooltip title="Remove Selected" placement="right">
            <IconButton
              size="small"
              onClick={onRemoveSelected}
              disabled={!hasSelection}
              color="error"
              sx={{ alignSelf: 'center' }}
            >
              <Delete />
            </IconButton>
          </Tooltip>

          {/* Clear Expired */}
          {hasExpiredItems && (
            <Tooltip title={`Clear ${expiredItemsCount} expired`} placement="right">
              <IconButton
                size="small"
                onClick={onClearExpired}
                color="warning"
                sx={{ alignSelf: 'center' }}
              >
                <Badge badgeContent={expiredItemsCount} color="warning" max={99}>
                  <CleaningServices />
                </Badge>
              </IconButton>
            </Tooltip>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default OperationsSidebar;
