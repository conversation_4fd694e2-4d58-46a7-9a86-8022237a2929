using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using VidCompressor.Models;
using System.Text.Json;

namespace VidCompressor.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class UserMediaItemsController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<UserMediaItemsController> _logger;

    public UserMediaItemsController(ApplicationDbContext context, ILogger<UserMediaItemsController> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Get all media items for the current user
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<UserMediaItemResponse>>> GetUserMediaItems()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        try
        {
            var mediaItems = await _context.UserMediaItems
                .Where(umi => umi.UserId == userId)
                .OrderByDescending(umi => umi.LastAccessedAt)
                .Select(umi => new UserMediaItemResponse
                {
                    Id = umi.Id,
                    GoogleMediaItemId = umi.GoogleMediaItemId,
                    Filename = umi.Filename,
                    MimeType = umi.MimeType,
                    BaseUrl = umi.BaseUrl,
                    MediaType = umi.MediaType,
                    Width = umi.Width,
                    Height = umi.Height,
                    CreationTime = umi.CreationTime,
                    Metadata = umi.Metadata,
                    AddedAt = umi.AddedAt,
                    LastAccessedAt = umi.LastAccessedAt,
                    LatestCompressionJob = _context.CompressionJobs
                        .Where(cj => cj.UserId == userId && cj.MediaItemId == umi.GoogleMediaItemId)
                        .OrderByDescending(cj => cj.CreatedAt)
                        .Select(cj => new CompressionJobSummary
                        {
                            Id = cj.Id,
                            Status = cj.Status,
                            Quality = cj.Quality,
                            UploadToGooglePhotos = cj.UploadToGooglePhotos,
                            CreatedAt = cj.CreatedAt,
                            CompletedAt = cj.CompletedAt,
                            ErrorMessage = cj.ErrorMessage,
                            OriginalSizeBytes = cj.OriginalSizeBytes,
                            CompressedSizeBytes = cj.CompressedSizeBytes,
                            CompressionRatio = cj.CompressionRatio
                        })
                        .FirstOrDefault()
                })
                .ToListAsync();

            // Update LastAccessedAt for all retrieved items
            var mediaItemIds = mediaItems.Select(mi => mi.Id).ToList();
            await _context.UserMediaItems
                .Where(umi => mediaItemIds.Contains(umi.Id))
                .ExecuteUpdateAsync(setters => setters.SetProperty(umi => umi.LastAccessedAt, DateTime.UtcNow));

            _logger.LogInformation("Retrieved {Count} media items for user {UserId}", mediaItems.Count, userId);
            return Ok(mediaItems);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve media items for user {UserId}", userId);
            return StatusCode(500, new { error = "Failed to retrieve media items", details = ex.Message });
        }
    }

    /// <summary>
    /// Save multiple media items for the current user
    /// </summary>
    [HttpPost("batch")]
    public async Task<ActionResult<IEnumerable<UserMediaItemResponse>>> SaveMediaItems([FromBody] List<UserMediaItemRequest> requests)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        if (requests == null || !requests.Any())
        {
            return BadRequest("No media items provided");
        }

        try
        {
            var savedItems = new List<UserMediaItem>();
            var now = DateTime.UtcNow;

            foreach (var request in requests)
            {
                // Check if this media item already exists for this user
                var existingItem = await _context.UserMediaItems
                    .FirstOrDefaultAsync(umi => umi.UserId == userId && umi.GoogleMediaItemId == request.GoogleMediaItemId);

                if (existingItem != null)
                {
                    // Update existing item's LastAccessedAt and metadata
                    existingItem.LastAccessedAt = now;
                    existingItem.Filename = request.Filename;
                    existingItem.MimeType = request.MimeType;
                    existingItem.BaseUrl = request.BaseUrl;
                    existingItem.Width = request.Width;
                    existingItem.Height = request.Height;
                    existingItem.CreationTime = request.CreationTime;
                    existingItem.Metadata = request.Metadata;
                    
                    savedItems.Add(existingItem);
                }
                else
                {
                    // Create new media item
                    var newItem = new UserMediaItem
                    {
                        UserId = userId,
                        GoogleMediaItemId = request.GoogleMediaItemId,
                        Filename = request.Filename,
                        MimeType = request.MimeType,
                        BaseUrl = request.BaseUrl,
                        MediaType = request.MediaType,
                        Width = request.Width,
                        Height = request.Height,
                        CreationTime = request.CreationTime,
                        Metadata = request.Metadata,
                        AddedAt = now,
                        LastAccessedAt = now
                    };

                    _context.UserMediaItems.Add(newItem);
                    savedItems.Add(newItem);
                }
            }

            await _context.SaveChangesAsync();

            // Convert to response DTOs
            var responses = savedItems.Select(item => new UserMediaItemResponse
            {
                Id = item.Id,
                GoogleMediaItemId = item.GoogleMediaItemId,
                Filename = item.Filename,
                MimeType = item.MimeType,
                BaseUrl = item.BaseUrl,
                MediaType = item.MediaType,
                Width = item.Width,
                Height = item.Height,
                CreationTime = item.CreationTime,
                Metadata = item.Metadata,
                AddedAt = item.AddedAt,
                LastAccessedAt = item.LastAccessedAt
            }).ToList();

            _logger.LogInformation("Saved {Count} media items for user {UserId}", savedItems.Count, userId);
            return Ok(responses);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save media items for user {UserId}", userId);
            return StatusCode(500, new { error = "Failed to save media items", details = ex.Message });
        }
    }

    /// <summary>
    /// Delete a specific media item for the current user
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteMediaItem(string id)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        try
        {
            var mediaItem = await _context.UserMediaItems
                .FirstOrDefaultAsync(umi => umi.Id == id && umi.UserId == userId);

            if (mediaItem == null)
            {
                return NotFound();
            }

            _context.UserMediaItems.Remove(mediaItem);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Deleted media item {MediaItemId} for user {UserId}", id, userId);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete media item {MediaItemId} for user {UserId}", id, userId);
            return StatusCode(500, new { error = "Failed to delete media item", details = ex.Message });
        }
    }

    /// <summary>
    /// Delete multiple media items for the current user
    /// </summary>
    [HttpDelete("batch")]
    public async Task<IActionResult> DeleteMediaItems([FromBody] List<string> ids)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        if (ids == null || !ids.Any())
        {
            return BadRequest("No media item IDs provided");
        }

        try
        {
            var deletedCount = await _context.UserMediaItems
                .Where(umi => ids.Contains(umi.Id) && umi.UserId == userId)
                .ExecuteDeleteAsync();

            _logger.LogInformation("Deleted {Count} media items for user {UserId}", deletedCount, userId);
            return Ok(new { deletedCount });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete media items for user {UserId}", userId);
            return StatusCode(500, new { error = "Failed to delete media items", details = ex.Message });
        }
    }

    /// <summary>
    /// Get a specific media item by Google Media Item ID
    /// </summary>
    [HttpGet("by-google-id/{googleMediaItemId}")]
    public async Task<ActionResult<UserMediaItemResponse>> GetMediaItemByGoogleId(string googleMediaItemId)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return Unauthorized();
        }

        try
        {
            var mediaItem = await _context.UserMediaItems
                .Where(umi => umi.UserId == userId && umi.GoogleMediaItemId == googleMediaItemId)
                .Select(umi => new UserMediaItemResponse
                {
                    Id = umi.Id,
                    GoogleMediaItemId = umi.GoogleMediaItemId,
                    Filename = umi.Filename,
                    MimeType = umi.MimeType,
                    BaseUrl = umi.BaseUrl,
                    MediaType = umi.MediaType,
                    Width = umi.Width,
                    Height = umi.Height,
                    CreationTime = umi.CreationTime,
                    Metadata = umi.Metadata,
                    AddedAt = umi.AddedAt,
                    LastAccessedAt = umi.LastAccessedAt,
                    LatestCompressionJob = _context.CompressionJobs
                        .Where(cj => cj.UserId == userId && cj.MediaItemId == umi.GoogleMediaItemId)
                        .OrderByDescending(cj => cj.CreatedAt)
                        .Select(cj => new CompressionJobSummary
                        {
                            Id = cj.Id,
                            Status = cj.Status,
                            Quality = cj.Quality,
                            UploadToGooglePhotos = cj.UploadToGooglePhotos,
                            CreatedAt = cj.CreatedAt,
                            CompletedAt = cj.CompletedAt,
                            ErrorMessage = cj.ErrorMessage,
                            OriginalSizeBytes = cj.OriginalSizeBytes,
                            CompressedSizeBytes = cj.CompressedSizeBytes,
                            CompressionRatio = cj.CompressionRatio
                        })
                        .FirstOrDefault()
                })
                .FirstOrDefaultAsync();

            if (mediaItem == null)
            {
                return NotFound();
            }

            // Update LastAccessedAt
            await _context.UserMediaItems
                .Where(umi => umi.UserId == userId && umi.GoogleMediaItemId == googleMediaItemId)
                .ExecuteUpdateAsync(setters => setters.SetProperty(umi => umi.LastAccessedAt, DateTime.UtcNow));

            return Ok(mediaItem);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve media item {GoogleMediaItemId} for user {UserId}", googleMediaItemId, userId);
            return StatusCode(500, new { error = "Failed to retrieve media item", details = ex.Message });
        }
    }
}
